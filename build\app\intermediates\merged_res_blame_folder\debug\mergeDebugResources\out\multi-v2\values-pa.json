{"logs": [{"outputFile": "com.get_lms_flutter.app-mergeDebugResources-60:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\1b7240aa81db29873b0daf3fe26fc3b1\\transformed\\core-1.13.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "29,30,31,32,33,34,35,95", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2767,2865,2967,3070,3171,3273,3371,9923", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "2860,2962,3065,3166,3268,3366,3495,10019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\6ceec6bff55e07b3979c7c6841be58f3\\transformed\\jetified-play-services-basement-18.2.0\\res\\values-pa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "67", "startColumns": "4", "startOffsets": "7083", "endColumns": "150", "endOffsets": "7229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\2d4414c41de30e1ee8f38e345744105f\\transformed\\preference-1.2.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,262,341,488,657,737", "endColumns": "71,84,78,146,168,79,77", "endOffsets": "172,257,336,483,652,732,810"}, "to": {"startLines": "77,88,92,93,96,97,98", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8337,9214,9617,9696,10024,10193,10273", "endColumns": "71,84,78,146,168,79,77", "endOffsets": "8404,9294,9691,9838,10188,10268,10346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\01c026ab9d98be5633d940cb3360b193\\transformed\\browser-1.8.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,261,375", "endColumns": "104,100,113,102", "endOffsets": "155,256,370,473"}, "to": {"startLines": "78,89,90,91", "startColumns": "4,4,4,4", "startOffsets": "8409,9299,9400,9514", "endColumns": "104,100,113,102", "endOffsets": "8509,9395,9509,9612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\aafc2bc7e0972df9911e370da464a911\\transformed\\jetified-play-services-base-18.0.1\\res\\values-pa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,591,696,839,964,1073,1172,1330,1435,1604,1732,1881,2038,2099,2161", "endColumns": "102,168,125,104,142,124,108,98,157,104,168,127,148,156,60,61,77", "endOffsets": "295,464,590,695,838,963,1072,1171,1329,1434,1603,1731,1880,2037,2098,2160,2238"}, "to": {"startLines": "59,60,61,62,63,64,65,66,68,69,70,71,72,73,74,75,76", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6072,6179,6352,6482,6591,6738,6867,6980,7234,7396,7505,7678,7810,7963,8124,8189,8255", "endColumns": "106,172,129,108,146,128,112,102,161,108,172,131,152,160,64,65,81", "endOffsets": "6174,6347,6477,6586,6733,6862,6975,7078,7391,7500,7673,7805,7958,8119,8184,8250,8332"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\146e143cf420799dfe6460ba9202d0fe\\transformed\\jetified-facebook-login-17.0.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,204,334,482,595,681,770,852,942,1031,1145,1259,1354,1449,1561,1692,1772,1858,2034,2129,2237,2352,2462", "endColumns": "148,129,147,112,85,88,81,89,88,113,113,94,94,111,130,79,85,175,94,107,114,109,164", "endOffsets": "199,329,477,590,676,765,847,937,1026,1140,1254,1349,1444,1556,1687,1767,1853,2029,2124,2232,2347,2457,2622"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3500,3649,3779,3927,4040,4126,4215,4297,4387,4476,4590,4704,4799,4894,5006,5137,5217,5303,5479,5574,5682,5797,5907", "endColumns": "148,129,147,112,85,88,81,89,88,113,113,94,94,111,130,79,85,175,94,107,114,109,164", "endOffsets": "3644,3774,3922,4035,4121,4210,4292,4382,4471,4585,4699,4794,4889,5001,5132,5212,5298,5474,5569,5677,5792,5902,6067"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\ba0a3358a2f1527f1f0bb638f3aae4bd\\transformed\\appcompat-1.6.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,94", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,9843", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,9918"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\1192a99ead1dcf451562e1b569f7ae4a\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,268,334,409,475,574,670", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "121,186,263,329,404,470,569,665,750"}, "to": {"startLines": "79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8514,8585,8650,8727,8793,8868,8934,9033,9129", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "8580,8645,8722,8788,8863,8929,9028,9124,9209"}}]}]}