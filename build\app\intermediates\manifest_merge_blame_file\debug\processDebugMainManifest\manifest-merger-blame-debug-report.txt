1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.get_lms_flutter"
4    android:versionCode="2"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:3:5-67
15-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:3:22-64
16
17    <queries>
17-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:5:5-19:15
18        <intent>
18-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:6:9-9:18
19            <action android:name="android.intent.action.VIEW" />
19-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:7:13-65
19-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:7:21-62
20
21            <data android:scheme="https" />
21-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:8:13-44
21-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:8:19-41
22        </intent>
23        <intent>
23-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:10:9-13:18
24            <action android:name="android.intent.action.DIAL" />
24-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:11:13-65
24-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:11:21-62
25
26            <data android:scheme="tel" />
26-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:8:13-44
26-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:8:19-41
27        </intent>
28        <intent>
28-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:14:9-17:18
29            <action android:name="android.intent.action.SEND" />
29-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:15:13-65
29-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:15:21-62
30
31            <data android:mimeType="*/*" />
31-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:8:13-44
32        </intent>
33
34        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />
34-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:18:9-89
34-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:18:19-86
35
36        <intent>
36-->[:flutter_downloader] C:\newapps\getlms\build\flutter_downloader\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:18
37            <action android:name="android.intent.action.VIEW" />
37-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:7:13-65
37-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:7:21-62
38        </intent>
39        <intent>
39-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
40            <action android:name="android.support.customtabs.action.CustomTabsService" />
40-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-90
40-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-87
41        </intent>
42
43        <package android:name="com.facebook.katana" />
43-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:18:9-55
43-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:18:18-52
44
45        <intent>
45-->[:file_picker] C:\newapps\getlms\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
46            <action android:name="android.intent.action.GET_CONTENT" />
46-->[:file_picker] C:\newapps\getlms\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
46-->[:file_picker] C:\newapps\getlms\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
47
48            <data android:mimeType="*/*" />
48-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:8:13-44
49        </intent>
50    </queries>
51
52    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
52-->[:flutter_downloader] C:\newapps\getlms\build\flutter_downloader\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-77
52-->[:flutter_downloader] C:\newapps\getlms\build\flutter_downloader\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:22-74
53    <uses-permission android:name="android.permission.WAKE_LOCK" />
53-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
53-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:22-65
54    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
54-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
54-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:22-76
55    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
55-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
55-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
56    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
56-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
56-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
57    <uses-permission android:name="android.permission.VIBRATE" />
57-->[:flutter_local_notifications] C:\newapps\getlms\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
57-->[:flutter_local_notifications] C:\newapps\getlms\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
58    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
58-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:16:5-79
58-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:16:22-76
59
60    <permission
60-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\1b7240aa81db29873b0daf3fe26fc3b1\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
61        android:name="com.get_lms_flutter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
61-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\1b7240aa81db29873b0daf3fe26fc3b1\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
62        android:protectionLevel="signature" />
62-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\1b7240aa81db29873b0daf3fe26fc3b1\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
63
64    <uses-permission android:name="com.get_lms_flutter.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
64-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\1b7240aa81db29873b0daf3fe26fc3b1\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
64-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\1b7240aa81db29873b0daf3fe26fc3b1\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
65    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
65-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fac557a9fc76be4938bcca5164f62d0a\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:5-110
65-->[com.android.installreferrer:installreferrer:1.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\fac557a9fc76be4938bcca5164f62d0a\transformed\jetified-installreferrer-1.0\AndroidManifest.xml:9:22-107
66
67    <application
68        android:name="android.app.Application"
69        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
69-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\1b7240aa81db29873b0daf3fe26fc3b1\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
70        android:debuggable="true"
71        android:extractNativeLibs="true"
72        android:icon="@mipmap/ic_launcher"
73        android:label="GetLMS"
74        android:supportsRtl="true"
74-->[com.facebook.android:facebook-login:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\146e143cf420799dfe6460ba9202d0fe\transformed\jetified-facebook-login-17.0.0\AndroidManifest.xml:16:18-44
75        android:usesCleartextTraffic="true" >
76        <activity
77            android:name="com.get_lms_flutter.MainActivity"
78            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
79            android:enableOnBackInvokedCallback="true"
80            android:exported="true"
81            android:hardwareAccelerated="true"
82            android:launchMode="singleTop"
83            android:theme="@style/LaunchTheme"
84            android:windowSoftInputMode="adjustResize" >
85
86            <!--
87                 Specifies an Android theme to apply to this Activity as soon as
88                 the Android process has started. This theme is visible to the user
89                 while the Flutter UI initializes. After that, this theme continues
90                 to determine the Window background behind the Flutter UI.
91            -->
92            <meta-data
93                android:name="io.flutter.embedding.android.NormalTheme"
94                android:resource="@style/NormalTheme" />
95            <meta-data
96                android:name="com.google.firebase.messaging.default_notification_channel_id"
97                android:value="get_lms" />
98
99            <intent-filter>
100                <action android:name="FLUTTER_NOTIFICATION_CLICK" />
101
102                <category android:name="android.intent.category.DEFAULT" />
102-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:34:17-76
102-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:34:27-73
103            </intent-filter>
104            <intent-filter>
105                <action android:name="android.intent.action.MAIN" />
106
107                <category android:name="android.intent.category.LAUNCHER" />
108            </intent-filter>
109        </activity>
110        <!--
111             Don't delete the meta-data below.
112             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
113        -->
114        <meta-data
115            android:name="flutterEmbedding"
116            android:value="2" />
117
118        <provider
118-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
119            android:name="androidx.startup.InitializationProvider"
119-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
120            android:authorities="com.get_lms_flutter.androidx-startup"
120-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
121            android:exported="false" >
121-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
122            <meta-data
122-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
123                android:name="androidx.work.WorkManagerInitializer"
123-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
124                android:value="androidx.startup" />
124-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
125            <meta-data
125-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\61517da98c6718d9df138b135b904c43\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
126                android:name="androidx.emoji2.text.EmojiCompatInitializer"
126-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\61517da98c6718d9df138b135b904c43\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
127                android:value="androidx.startup" />
127-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\61517da98c6718d9df138b135b904c43\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
128            <meta-data
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\5a2470dc96d156369215512b046d1fa1\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
129                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\5a2470dc96d156369215512b046d1fa1\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
130                android:value="androidx.startup" />
130-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\5a2470dc96d156369215512b046d1fa1\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
131            <meta-data
131-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
132                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
132-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
133                android:value="androidx.startup" />
133-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
134        </provider>
135
136        <service
136-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
137            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
137-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
138            android:directBootAware="false"
138-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
139            android:enabled="@bool/enable_system_alarm_service_default"
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
140            android:exported="false" />
140-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
141        <service
141-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
142            android:name="androidx.work.impl.background.systemjob.SystemJobService"
142-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
143            android:directBootAware="false"
143-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
144            android:enabled="@bool/enable_system_job_service_default"
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
145            android:exported="true"
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
146            android:permission="android.permission.BIND_JOB_SERVICE" />
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
147        <service
147-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
148            android:name="androidx.work.impl.foreground.SystemForegroundService"
148-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
149            android:directBootAware="false"
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
150            android:enabled="@bool/enable_system_foreground_service_default"
150-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
151            android:exported="false" />
151-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
152
153        <receiver
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
154            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
154-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
155            android:directBootAware="false"
155-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
156            android:enabled="true"
156-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
157            android:exported="false" />
157-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
158        <receiver
158-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
159            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
159-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
160            android:directBootAware="false"
160-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
161            android:enabled="false"
161-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
162            android:exported="false" >
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
163            <intent-filter>
163-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
164                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
165                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
166            </intent-filter>
167        </receiver>
168        <receiver
168-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
169            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
170            android:directBootAware="false"
170-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
171            android:enabled="false"
171-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
172            android:exported="false" >
172-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
173            <intent-filter>
173-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
174                <action android:name="android.intent.action.BATTERY_OKAY" />
174-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
174-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
175                <action android:name="android.intent.action.BATTERY_LOW" />
175-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
175-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
176            </intent-filter>
177        </receiver>
178        <receiver
178-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
179            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
179-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
180            android:directBootAware="false"
180-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
181            android:enabled="false"
181-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
182            android:exported="false" >
182-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
183            <intent-filter>
183-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
184                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
184-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
184-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
185                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
185-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
185-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
186            </intent-filter>
187        </receiver>
188        <receiver
188-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
189            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
189-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
190            android:directBootAware="false"
190-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
191            android:enabled="false"
191-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
192            android:exported="false" >
192-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
193            <intent-filter>
193-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
194                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
194-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
194-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
195            </intent-filter>
196        </receiver>
197        <receiver
197-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
198            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
198-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
199            android:directBootAware="false"
199-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
200            android:enabled="false"
200-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
201            android:exported="false" >
201-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
202            <intent-filter>
202-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
203                <action android:name="android.intent.action.BOOT_COMPLETED" />
203-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
203-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
204                <action android:name="android.intent.action.TIME_SET" />
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
205                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
205-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
205-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
206            </intent-filter>
207        </receiver>
208        <receiver
208-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
209            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
209-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
210            android:directBootAware="false"
210-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
211            android:enabled="@bool/enable_system_alarm_service_default"
211-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
212            android:exported="false" >
212-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
213            <intent-filter>
213-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
214                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
214-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
214-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
215            </intent-filter>
216        </receiver>
217        <receiver
217-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
218            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
218-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
219            android:directBootAware="false"
219-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
220            android:enabled="true"
220-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
221            android:exported="true"
221-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
222            android:permission="android.permission.DUMP" >
222-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
223            <intent-filter>
223-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
224                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
224-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
224-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f57fa92900567520c1e8b40c26679247\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
225            </intent-filter>
226        </receiver>
227
228        <service
228-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f618b9b384671e771b2500620ba8d8d1\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
229            android:name="androidx.room.MultiInstanceInvalidationService"
229-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f618b9b384671e771b2500620ba8d8d1\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
230            android:directBootAware="true"
230-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f618b9b384671e771b2500620ba8d8d1\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
231            android:exported="false" />
231-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\f618b9b384671e771b2500620ba8d8d1\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
232
233        <activity
233-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:47
234            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
234-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-112
235            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
235-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-137
236            android:exported="false"
236-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
237            android:theme="@style/AppTheme" />
237-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-44
238        <activity
238-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-22:55
239            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
239-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-120
240            android:exported="false"
240-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-37
241            android:theme="@style/ThemeTransparent" />
241-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-52
242        <activity
242-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-26:55
243            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
243-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-114
244            android:exported="false"
244-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-37
245            android:theme="@style/ThemeTransparent" />
245-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-52
246        <activity
246-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:9-31:55
247            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
247-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-134
248            android:exported="false"
248-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-37
249            android:launchMode="singleInstance"
249-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-48
250            android:theme="@style/ThemeTransparent" />
250-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-52
251        <activity
251-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:9-36:55
252            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
252-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-128
253            android:exported="false"
253-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-37
254            android:launchMode="singleInstance"
254-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-48
255            android:theme="@style/ThemeTransparent" />
255-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-52
256
257        <receiver
257-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-41:40
258            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
258-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-119
259            android:enabled="true"
259-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-35
260            android:exported="false" />
260-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
261
262        <meta-data
262-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:9-45:36
263            android:name="io.flutter.embedded_views_preview"
263-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-61
264            android:value="true" />
264-->[:flutter_inappwebview_android] C:\newapps\getlms\build\flutter_inappwebview_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-33
265
266        <activity
266-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:22:9-25:66
267            android:name="com.facebook.FacebookActivity"
267-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:23:13-57
268            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
268-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:24:13-96
269            android:theme="@style/com_facebook_activity_theme" />
269-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:25:13-63
270        <activity android:name="com.facebook.CustomTabMainActivity" />
270-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:26:9-71
270-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:26:19-68
271        <activity
271-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:27:9-41:20
272            android:name="com.facebook.CustomTabActivity"
272-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:28:13-58
273            android:exported="true" >
273-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:29:13-36
274            <intent-filter>
274-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:31:13-40:29
275                <action android:name="android.intent.action.VIEW" />
275-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:7:13-65
275-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:7:21-62
276
277                <category android:name="android.intent.category.DEFAULT" />
277-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:34:17-76
277-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:34:27-73
278                <category android:name="android.intent.category.BROWSABLE" />
278-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:35:17-78
278-->[com.facebook.android:facebook-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\e6b74ce4bd69e43a9b32319e73a78fd2\transformed\jetified-facebook-common-17.0.0\AndroidManifest.xml:35:27-75
279
280                <data
280-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:8:13-44
281                    android:host="cct.com.get_lms_flutter"
282                    android:scheme="fbconnect" />
282-->C:\newapps\getlms\android\app\src\main\AndroidManifest.xml:8:19-41
283            </intent-filter>
284        </activity>
285
286        <provider
286-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
287            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
287-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
288            android:authorities="com.get_lms_flutter.flutter.image_provider"
288-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
289            android:exported="false"
289-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
290            android:grantUriPermissions="true" >
290-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
291            <meta-data
291-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
292                android:name="android.support.FILE_PROVIDER_PATHS"
292-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
293                android:resource="@xml/flutter_image_picker_file_paths" />
293-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
294        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
295        <service
295-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
296            android:name="com.google.android.gms.metadata.ModuleDependencies"
296-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
297            android:enabled="false"
297-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
298            android:exported="false" >
298-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
299            <intent-filter>
299-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
300                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
300-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
300-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
301            </intent-filter>
302
303            <meta-data
303-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
304                android:name="photopicker_activity:0:required"
304-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
305                android:value="" />
305-->[:image_picker_android] C:\newapps\getlms\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
306        </service>
307
308        <activity
308-->[:url_launcher_android] C:\newapps\getlms\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
309            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
309-->[:url_launcher_android] C:\newapps\getlms\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
310            android:exported="false"
310-->[:url_launcher_android] C:\newapps\getlms\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
311            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
311-->[:url_launcher_android] C:\newapps\getlms\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
312
313        <uses-library
313-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6628e919b5a5d75d6868684452c1191d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
314            android:name="androidx.window.extensions"
314-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6628e919b5a5d75d6868684452c1191d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
315            android:required="false" />
315-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6628e919b5a5d75d6868684452c1191d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
316        <uses-library
316-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6628e919b5a5d75d6868684452c1191d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
317            android:name="androidx.window.sidecar"
317-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6628e919b5a5d75d6868684452c1191d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
318            android:required="false" />
318-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6628e919b5a5d75d6868684452c1191d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
319
320        <activity
320-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\0fc694d8c6ed625e42a8ec3a09da0292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
321            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
321-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\0fc694d8c6ed625e42a8ec3a09da0292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
322            android:excludeFromRecents="true"
322-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\0fc694d8c6ed625e42a8ec3a09da0292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
323            android:exported="false"
323-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\0fc694d8c6ed625e42a8ec3a09da0292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
324            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
324-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\0fc694d8c6ed625e42a8ec3a09da0292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
325        <!--
326            Service handling Google Sign-In user revocation. For apps that do not integrate with
327            Google Sign-In, this service will never be started.
328        -->
329        <service
329-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\0fc694d8c6ed625e42a8ec3a09da0292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
330            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
330-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\0fc694d8c6ed625e42a8ec3a09da0292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
331            android:exported="true"
331-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\0fc694d8c6ed625e42a8ec3a09da0292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
332            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
332-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\0fc694d8c6ed625e42a8ec3a09da0292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
333            android:visibleToInstantApps="true" />
333-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\0fc694d8c6ed625e42a8ec3a09da0292\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
334
335        <activity
335-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\aafc2bc7e0972df9911e370da464a911\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
336            android:name="com.google.android.gms.common.api.GoogleApiActivity"
336-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\aafc2bc7e0972df9911e370da464a911\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
337            android:exported="false"
337-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\aafc2bc7e0972df9911e370da464a911\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
338            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
338-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.8\transforms\aafc2bc7e0972df9911e370da464a911\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
339
340        <meta-data
340-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6ceec6bff55e07b3979c7c6841be58f3\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
341            android:name="com.google.android.gms.version"
341-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6ceec6bff55e07b3979c7c6841be58f3\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
342            android:value="@integer/google_play_services_version" />
342-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\6ceec6bff55e07b3979c7c6841be58f3\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
343        <!--
344         The initialization ContentProvider will call FacebookSdk.sdkInitialize automatically
345         with the application context. This config is merged in with the host app's manifest,
346         but there can only be one provider with the same authority activated at any given
347         point; so if the end user has two or more different apps that use Facebook SDK, only the
348         first one will be able to use the provider. To work around this problem, we use the
349         following placeholder in the authority to identify each host application as if it was
350         a completely different provider.
351        -->
352        <provider
352-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:29:9-32:40
353            android:name="com.facebook.internal.FacebookInitProvider"
353-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:30:13-70
354            android:authorities="com.get_lms_flutter.FacebookInitProvider"
354-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:31:13-72
355            android:exported="false" />
355-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:32:13-37
356
357        <receiver
357-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:34:9-40:20
358            android:name="com.facebook.CurrentAccessTokenExpirationBroadcastReceiver"
358-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:35:13-86
359            android:exported="false" >
359-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:36:13-37
360            <intent-filter>
360-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:37:13-39:29
361                <action android:name="com.facebook.sdk.ACTION_CURRENT_ACCESS_TOKEN_CHANGED" />
361-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:38:17-95
361-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:38:25-92
362            </intent-filter>
363        </receiver>
364        <receiver
364-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:41:9-47:20
365            android:name="com.facebook.AuthenticationTokenManager$CurrentAuthenticationTokenChangedBroadcastReceiver"
365-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:42:13-118
366            android:exported="false" >
366-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:43:13-37
367            <intent-filter>
367-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:44:13-46:29
368                <action android:name="com.facebook.sdk.ACTION_CURRENT_AUTHENTICATION_TOKEN_CHANGED" />
368-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:45:17-103
368-->[com.facebook.android:facebook-core:17.0.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\1f1fb6e4476c972954eab0e04d48c4ee\transformed\jetified-facebook-core-17.0.0\AndroidManifest.xml:45:25-100
369            </intent-filter>
370        </receiver>
371        <receiver
371-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
372            android:name="androidx.profileinstaller.ProfileInstallReceiver"
372-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
373            android:directBootAware="false"
373-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
374            android:enabled="true"
374-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
375            android:exported="true"
375-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
376            android:permission="android.permission.DUMP" >
376-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
377            <intent-filter>
377-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
378                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
378-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
378-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
379            </intent-filter>
380            <intent-filter>
380-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
381                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
381-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
381-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
382            </intent-filter>
383            <intent-filter>
383-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
384                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
384-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
384-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
385            </intent-filter>
386            <intent-filter>
386-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
387                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
387-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
387-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.8\transforms\a5f03e4245efb8670cc2e790defb2025\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
388            </intent-filter>
389        </receiver>
390    </application>
391
392</manifest>
