{"logs": [{"outputFile": "com.get_lms_flutter.app-mergeDebugResources-60:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\2d4414c41de30e1ee8f38e345744105f\\transformed\\preference-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,482,651,730", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "170,257,335,477,646,725,801"}, "to": {"startLines": "76,87,91,92,95,96,97", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8091,8964,9361,9439,9762,9931,10010", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "8156,9046,9434,9576,9926,10005,10081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\aafc2bc7e0972df9911e370da464a911\\transformed\\jetified-play-services-base-18.0.1\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "58,59,60,61,62,63,64,65,67,68,69,70,71,72,73,74,75", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5877,5983,6142,6268,6377,6533,6663,6783,7016,7170,7277,7438,7566,7708,7884,7951,8013", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "5978,6137,6263,6372,6528,6658,6778,6881,7165,7272,7433,7561,7703,7879,7946,8008,8086"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\6ceec6bff55e07b3979c7c6841be58f3\\transformed\\jetified-play-services-basement-18.2.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "66", "startColumns": "4", "startOffsets": "6886", "endColumns": "129", "endOffsets": "7011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\146e143cf420799dfe6460ba9202d0fe\\transformed\\jetified-facebook-login-17.0.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,217,362,501,612,693,779,860,949,1040,1153,1262,1353,1444,1545,1661,1741,1914,2011,2111,2224,2332", "endColumns": "161,144,138,110,80,85,80,88,90,112,108,90,90,100,115,79,172,96,99,112,107,136", "endOffsets": "212,357,496,607,688,774,855,944,1035,1148,1257,1348,1439,1540,1656,1736,1909,2006,2106,2219,2327,2464"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3463,3625,3770,3909,4020,4101,4187,4268,4357,4448,4561,4670,4761,4852,4953,5069,5149,5322,5419,5519,5632,5740", "endColumns": "161,144,138,110,80,85,80,88,90,112,108,90,90,100,115,79,172,96,99,112,107,136", "endOffsets": "3620,3765,3904,4015,4096,4182,4263,4352,4443,4556,4665,4756,4847,4948,5064,5144,5317,5414,5514,5627,5735,5872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\01c026ab9d98be5633d940cb3360b193\\transformed\\browser-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "77,88,89,90", "startColumns": "4,4,4,4", "startOffsets": "8161,9051,9152,9264", "endColumns": "109,100,111,96", "endOffsets": "8266,9147,9259,9356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\1b7240aa81db29873b0daf3fe26fc3b1\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "29,30,31,32,33,34,35,94", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2737,2831,2933,3030,3129,3237,3343,9661", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "2826,2928,3025,3124,3232,3338,3458,9757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\1192a99ead1dcf451562e1b569f7ae4a\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,257,328,408,486,580,677", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "126,188,252,323,403,481,575,672,743"}, "to": {"startLines": "78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8271,8347,8409,8473,8544,8624,8702,8796,8893", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "8342,8404,8468,8539,8619,8697,8791,8888,8959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\ba0a3358a2f1527f1f0bb638f3aae4bd\\transformed\\appcompat-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,9581", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,9656"}}]}]}