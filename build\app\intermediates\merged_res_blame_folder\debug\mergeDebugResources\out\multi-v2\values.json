{"logs": [{"outputFile": "com.get_lms_flutter.app-mergeDebugResources-60:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\1192a99ead1dcf451562e1b569f7ae4a\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "500,501,502,503,504,505,506,507,508", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "32748,32818,32880,32945,33009,33086,33151,33241,33325", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "32813,32875,32940,33004,33081,33146,33236,33320,33389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\ba0a3358a2f1527f1f0bb638f3aae4bd\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "5,28,29,60,61,62,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,85,86,91,92,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,274,275,279,280,281,282,283,284,285,315,316,317,318,319,320,321,322,358,359,360,361,366,374,375,380,402,409,410,411,412,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,526,531,532,533,534,535,539,547,548,552,556,567,572,578,585,589,593,598,602,606,610,614,618,622,628,632,638,642,648,652,657,661,664,668,674,678,684,688,694,697,701,705,709,713,717,718,719,720,723,726,729,732,736,737,738,739,740,743,745,747,749,754,755,759,765,769,770,772,784,785,789,795,799,800,801,805,832,836,837,841,869,1041,1067,1238,1264,1295,1303,1309,1325,1347,1352,1357,1367,1376,1385,1389,1396,1415,1422,1423,1432,1435,1438,1442,1446,1450,1453,1454,1459,1464,1474,1479,1486,1492,1493,1496,1500,1505,1507,1509,1512,1515,1517,1521,1524,1531,1534,1537,1541,1543,1547,1549,1551,1553,1557,1565,1573,1585,1591,1600,1603,1614,1617,1618,1623,1624,1664,1733,1803,1804,1814,1823,1975,1977,1981,1984,1987,1990,1993,1996,1999,2002,2006,2009,2012,2015,2019,2022,2026,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2052,2054,2055,2056,2057,2058,2059,2060,2061,2063,2064,2066,2067,2069,2071,2072,2074,2075,2076,2077,2078,2079,2081,2082,2083,2084,2085,2102,2104,2106,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2122,2123,2124,2125,2126,2127,2128,2130,2134,2138,2139,2140,2141,2142,2143,2147,2148,2149,2158,2160,2162,2164,2166,2168,2169,2170,2171,2173,2175,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2188,2191,2192,2193,2194,2196,2198,2199,2201,2202,2204,2206,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2221,2222,2223,2224,2226,2227,2228,2229,2230,2232,2234,2236,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2303,2378,2381,2384,2387,2401,2418,2460,2463,2492,2519,2528,2592,2960,3009,3047,3185,3309,3333,3339,3368,3389,3513,3541,3547,3691,3717,3784,3859,3959,3979,4034,4046,4072", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,988,1033,2080,2121,2176,2571,2635,2705,2766,2841,2917,2994,3232,3317,3399,3475,3551,3628,3706,3812,3918,3997,4326,4383,6365,6439,6514,6579,6645,6705,6766,6838,6911,6978,7046,7105,7164,7223,7282,7341,7395,7449,7502,7556,7610,7664,8008,8082,8161,8234,8308,8379,8451,8523,8596,8653,8711,8784,8858,8932,9007,9079,9152,9222,9293,9353,9414,9483,9552,9622,9696,9772,9836,9913,9989,10066,10131,10200,10277,10352,10421,10489,10566,10632,10693,10790,10855,10924,11023,11094,11153,11211,11268,11327,11391,11462,11534,11606,11678,11750,11817,11885,11953,12012,12075,12139,12229,12320,12380,12446,12513,12579,12649,12713,12766,12833,12894,12961,13074,13132,13195,13260,13325,13400,13473,13545,13589,13636,13682,13731,13792,13853,13914,13976,14040,14104,14168,14233,14296,14356,14417,14483,14542,14602,14664,14735,14795,16829,16915,17165,17255,17342,17430,17512,17595,17685,19622,19674,19732,19777,19843,19907,19964,20021,22198,22255,22303,22352,22607,22977,23024,23282,24453,24799,24863,24925,24985,25306,25380,25450,25528,25582,25652,25737,25785,25831,25892,25955,26021,26085,26156,26219,26284,26348,26409,26470,26522,26595,26669,26738,26813,26887,26961,27102,34551,34912,34990,35080,35168,35264,35427,36009,36098,36345,36626,37292,37577,37970,38447,38669,38891,39167,39394,39624,39854,40084,40314,40541,40960,41186,41611,41841,42269,42488,42771,42979,43110,43337,43763,43988,44415,44636,45061,45181,45457,45758,46082,46373,46687,46824,46955,47060,47302,47469,47673,47881,48152,48264,48376,48481,48598,48812,48958,49098,49184,49532,49620,49866,50284,50533,50615,50713,51370,51470,51722,52146,52401,52495,52584,52821,54845,55087,55189,55442,57598,68279,69795,80490,82018,83775,84401,84821,86082,87347,87603,87839,88386,88880,89485,89683,90263,91631,92006,92124,92662,92819,93015,93288,93544,93714,93855,93919,94284,94651,95327,95591,95929,96282,96376,96562,96868,97130,97255,97382,97621,97832,97951,98144,98321,98776,98957,99079,99338,99451,99638,99740,99847,99976,100251,100759,101255,102132,102426,102996,103145,103877,104049,104133,104469,104561,107021,112252,117623,117685,118263,118847,126794,126907,127136,127296,127448,127619,127785,127954,128121,128284,128527,128697,128870,129041,129315,129514,129719,130049,130133,130229,130325,130423,130523,130625,130727,130829,130931,131033,131133,131229,131341,131470,131593,131724,131855,131953,132067,132161,132301,132435,132531,132643,132743,132859,132955,133067,133167,133307,133443,133607,133737,133895,134045,134186,134330,134465,134577,134727,134855,134983,135119,135251,135381,135511,135623,136903,137049,137193,137331,137397,137487,137563,137667,137757,137859,137967,138075,138175,138255,138347,138445,138555,138607,138685,138791,138883,138987,139097,139219,139382,139539,139619,139719,139809,139919,140009,140250,140344,140450,140994,141094,141206,141320,141436,141552,141646,141760,141872,141974,142094,142216,142298,142402,142522,142648,142746,142840,142928,143040,143156,143278,143390,143565,143681,143767,143859,143971,144095,144162,144288,144356,144484,144628,144756,144825,144920,145035,145148,145247,145356,145467,145578,145679,145784,145884,146014,146105,146228,146322,146434,146520,146624,146720,146808,146926,147030,147134,147260,147348,147456,147556,147646,147756,147840,147942,148026,148080,148144,148250,148336,148446,148530,151473,154089,154207,154322,154402,154763,155349,156753,156831,158175,159536,159924,162767,173005,174668,176339,183152,187453,188204,188466,189313,189692,193970,194824,195053,199661,200671,202623,205194,209318,210062,212193,212533,213844", "endLines": "5,28,29,60,61,62,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,85,86,91,92,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,274,275,279,280,281,282,283,284,285,315,316,317,318,319,320,321,322,358,359,360,361,366,374,375,380,402,409,410,411,412,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,526,531,532,533,534,535,546,547,551,555,559,571,577,584,588,592,597,601,605,609,613,617,621,627,631,637,641,647,651,656,660,663,667,673,677,683,687,693,696,700,704,708,712,716,717,718,719,722,725,728,731,735,736,737,738,739,742,744,746,748,753,754,758,764,768,769,771,783,784,788,794,798,799,800,804,831,835,836,840,868,1040,1066,1237,1263,1294,1302,1308,1324,1346,1351,1356,1366,1375,1384,1388,1395,1414,1421,1422,1431,1434,1437,1441,1445,1449,1452,1453,1458,1463,1473,1478,1485,1491,1492,1495,1499,1504,1506,1508,1511,1514,1516,1520,1523,1530,1533,1536,1540,1542,1546,1548,1550,1552,1556,1564,1572,1584,1590,1599,1602,1613,1616,1617,1622,1623,1628,1732,1802,1803,1813,1822,1823,1976,1980,1983,1986,1989,1992,1995,1998,2001,2005,2008,2011,2014,2018,2021,2025,2029,2030,2031,2032,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2051,2053,2054,2055,2056,2057,2058,2059,2060,2062,2063,2065,2066,2068,2070,2071,2073,2074,2075,2076,2077,2078,2080,2081,2082,2083,2084,2085,2103,2105,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2121,2122,2123,2124,2125,2126,2127,2129,2133,2137,2138,2139,2140,2141,2142,2146,2147,2148,2149,2159,2161,2163,2165,2167,2168,2169,2170,2172,2174,2176,2177,2178,2179,2180,2181,2182,2183,2184,2185,2186,2187,2190,2191,2192,2193,2195,2197,2198,2200,2201,2203,2205,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2220,2221,2222,2223,2225,2226,2227,2228,2229,2231,2233,2235,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2377,2380,2383,2386,2400,2406,2427,2462,2491,2518,2527,2591,2954,2963,3036,3074,3202,3332,3338,3344,3388,3512,3532,3546,3550,3696,3751,3795,3924,3978,4033,4045,4071,4078", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "372,1028,1077,2116,2171,2233,2630,2700,2761,2836,2912,2989,3067,3312,3394,3470,3546,3623,3701,3807,3913,3992,4072,4378,4436,6434,6509,6574,6640,6700,6761,6833,6906,6973,7041,7100,7159,7218,7277,7336,7390,7444,7497,7551,7605,7659,7713,8077,8156,8229,8303,8374,8446,8518,8591,8648,8706,8779,8853,8927,9002,9074,9147,9217,9288,9348,9409,9478,9547,9617,9691,9767,9831,9908,9984,10061,10126,10195,10272,10347,10416,10484,10561,10627,10688,10785,10850,10919,11018,11089,11148,11206,11263,11322,11386,11457,11529,11601,11673,11745,11812,11880,11948,12007,12070,12134,12224,12315,12375,12441,12508,12574,12644,12708,12761,12828,12889,12956,13069,13127,13190,13255,13320,13395,13468,13540,13584,13631,13677,13726,13787,13848,13909,13971,14035,14099,14163,14228,14291,14351,14412,14478,14537,14597,14659,14730,14790,14858,16910,16997,17250,17337,17425,17507,17590,17680,17771,19669,19727,19772,19838,19902,19959,20016,20070,22250,22298,22347,22398,22636,23019,23068,23323,24480,24858,24920,24980,25037,25375,25445,25523,25577,25647,25732,25780,25826,25887,25950,26016,26080,26151,26214,26279,26343,26404,26465,26517,26590,26664,26733,26808,26882,26956,27097,27167,34599,34985,35075,35163,35259,35349,36004,36093,36340,36621,36873,37572,37965,38442,38664,38886,39162,39389,39619,39849,40079,40309,40536,40955,41181,41606,41836,42264,42483,42766,42974,43105,43332,43758,43983,44410,44631,45056,45176,45452,45753,46077,46368,46682,46819,46950,47055,47297,47464,47668,47876,48147,48259,48371,48476,48593,48807,48953,49093,49179,49527,49615,49861,50279,50528,50610,50708,51365,51465,51717,52141,52396,52490,52579,52816,54840,55082,55184,55437,57593,68274,69790,80485,82013,83770,84396,84816,86077,87342,87598,87834,88381,88875,89480,89678,90258,91626,92001,92119,92657,92814,93010,93283,93539,93709,93850,93914,94279,94646,95322,95586,95924,96277,96371,96557,96863,97125,97250,97377,97616,97827,97946,98139,98316,98771,98952,99074,99333,99446,99633,99735,99842,99971,100246,100754,101250,102127,102421,102991,103140,103872,104044,104128,104464,104556,104834,112247,117618,117680,118258,118842,118933,126902,127131,127291,127443,127614,127780,127949,128116,128279,128522,128692,128865,129036,129310,129509,129714,130044,130128,130224,130320,130418,130518,130620,130722,130824,130926,131028,131128,131224,131336,131465,131588,131719,131850,131948,132062,132156,132296,132430,132526,132638,132738,132854,132950,133062,133162,133302,133438,133602,133732,133890,134040,134181,134325,134460,134572,134722,134850,134978,135114,135246,135376,135506,135618,135758,137044,137188,137326,137392,137482,137558,137662,137752,137854,137962,138070,138170,138250,138342,138440,138550,138602,138680,138786,138878,138982,139092,139214,139377,139534,139614,139714,139804,139914,140004,140245,140339,140445,140537,141089,141201,141315,141431,141547,141641,141755,141867,141969,142089,142211,142293,142397,142517,142643,142741,142835,142923,143035,143151,143273,143385,143560,143676,143762,143854,143966,144090,144157,144283,144351,144479,144623,144751,144820,144915,145030,145143,145242,145351,145462,145573,145674,145779,145879,146009,146100,146223,146317,146429,146515,146619,146715,146803,146921,147025,147129,147255,147343,147451,147551,147641,147751,147835,147937,148021,148075,148139,148245,148331,148441,148525,148645,154084,154202,154317,154397,154758,154991,155861,156826,158170,159531,159919,162762,172815,173135,176033,177691,183719,188199,188461,188661,189687,193965,194571,195048,195199,199871,201749,202930,208215,210057,212188,212528,213839,214042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\6ceec6bff55e07b3979c7c6841be58f3\\transformed\\jetified-play-services-basement-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "413,488", "startColumns": "4,4", "startOffsets": "25042,31349", "endColumns": "67,166", "endOffsets": "25105,31511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\3ca619af96a941ca17a7cca74f58ea8a\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "363,379,408,3261,3266", "startColumns": "4,4,4,4,4", "startOffsets": "22463,23217,24735,186285,186455", "endLines": "363,379,408,3265,3269", "endColumns": "56,64,63,24,24", "endOffsets": "22515,23277,24794,186450,186599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\32e98afbd39bc05432a50d5754087209\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "406", "startColumns": "4", "startOffsets": "24631", "endColumns": "53", "endOffsets": "24680"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\8eac78d4526d90750f03e4412b4f26b8\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "407", "startColumns": "4", "startOffsets": "24685", "endColumns": "49", "endOffsets": "24730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\e6b74ce4bd69e43a9b32319e73a78fd2\\transformed\\jetified-facebook-common-17.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,49,54,60,71,82,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,202,290,370,449,513,590,665,732,814,895,968,1023,1092,1171,1235,1309,1382,1455,1527,1600,1673,1738,1807,4164,4458,4808,5429,6060,6239", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,53,59,70,81,84,112", "endColumns": "51,87,79,78,63,76,74,66,81,80,72,54,68,78,63,73,72,72,71,72,72,64,68,64,12,12,12,12,12,24", "endOffsets": "197,285,365,444,508,585,660,727,809,890,963,1018,1087,1166,1230,1304,1377,1450,1522,1595,1668,1733,1802,1867,4453,4803,5424,6055,6234,7468"}, "to": {"startLines": "99,100,101,102,103,104,105,106,107,108,109,110,251,252,253,255,256,257,258,259,260,261,262,263,2258,2263,2269,2280,2291,4079", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4821,4873,4961,5041,5120,5184,5261,5336,5403,5485,5566,5639,15176,15245,15324,15458,15532,15605,15678,15750,15823,15896,15961,16030,148934,149228,149578,150199,150830,214047", "endLines": "99,100,101,102,103,104,105,106,107,108,109,110,251,252,253,255,256,257,258,259,260,261,262,263,2262,2268,2279,2290,2293,4106", "endColumns": "51,87,79,78,63,76,74,66,81,80,72,54,68,78,63,73,72,72,71,72,72,64,68,64,12,12,12,12,12,24", "endOffsets": "4868,4956,5036,5115,5179,5256,5331,5398,5480,5561,5634,5689,15240,15319,15383,15527,15600,15673,15745,15818,15891,15956,16025,16090,149223,149573,150194,150825,151004,215276"}}, {"source": "C:\\newapps\\getlms\\build\\flutter_downloader\\intermediates\\packaged_res\\debug\\packageDebugResources\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,133,239,323,401,475,559,633", "endColumns": "77,105,83,77,73,83,73,75", "endOffsets": "128,234,318,396,470,554,628,704"}, "to": {"startLines": "513,514,515,516,517,518,519,520", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "33666,33744,33850,33934,34012,34086,34170,34244", "endColumns": "77,105,83,77,73,83,73,75", "endOffsets": "33739,33845,33929,34007,34081,34165,34239,34315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\5329643f68fdf85e79974bc93f42b948\\transformed\\jetified-core-viewtree-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "403", "startColumns": "4", "startOffsets": "24485", "endColumns": "42", "endOffsets": "24523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\aafc2bc7e0972df9911e370da464a911\\transformed\\jetified-play-services-base-18.0.1\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "111,112,113,114,115,116,117,118,480,481,482,483,484,485,486,487,489,490,491,492,493,494,495,496,497,3355,3765", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5694,5784,5864,5954,6044,6124,6205,6285,30309,30414,30595,30720,30827,31007,31130,31246,31516,31704,31809,31990,32115,32290,32438,32501,32563,188998,202206", "endLines": "111,112,113,114,115,116,117,118,480,481,482,483,484,485,486,487,489,490,491,492,493,494,495,496,497,3367,3783", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "5779,5859,5949,6039,6119,6200,6280,6360,30409,30590,30715,30822,31002,31125,31241,31344,31699,31804,31985,32110,32285,32433,32496,32558,32637,189308,202618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\146e143cf420799dfe6460ba9202d0fe\\transformed\\jetified-facebook-login-17.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,38,51", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,175,257,340,421,558,673,791,886,951,1019,1077,1149,1221,1318,1409,1483,1557,1670,1771,1834,2054,2219,2296,2378,2503,2589,2713,2799,3177,3880", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,37,50,59", "endColumns": "69,81,82,80,136,114,117,94,64,67,57,71,71,96,90,73,73,112,100,62,219,164,76,81,124,85,123,85,12,24,24", "endOffsets": "170,252,335,416,553,668,786,881,946,1014,1072,1144,1216,1313,1404,1478,1552,1665,1766,1829,2049,2214,2291,2373,2498,2584,2708,2794,3172,3875,4296"}, "to": {"startLines": "254,264,265,266,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,2294,2295,4107,4120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15388,16095,16177,16260,28017,28154,28269,28387,28482,28547,28615,28673,28745,28817,28914,29005,29079,29153,29266,29367,29430,29650,29815,29892,29974,30099,30185,151009,151095,215281,215984", "endLines": "254,264,265,266,457,458,459,460,461,462,463,464,465,466,467,468,469,470,471,472,473,474,475,476,477,478,479,2294,2302,4119,4128", "endColumns": "69,81,82,80,136,114,117,94,64,67,57,71,71,96,90,73,73,112,100,62,219,164,76,81,124,85,123,85,12,24,24", "endOffsets": "15453,16172,16255,16336,28149,28264,28382,28477,28542,28610,28668,28740,28812,28909,29000,29074,29148,29261,29362,29425,29645,29810,29887,29969,30094,30180,30304,151090,151468,215979,216400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\dc29b71ece44c3c389db74daf9d5bc5d\\transformed\\jetified-activity-1.10.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "376,405", "startColumns": "4,4", "startOffsets": "23073,24571", "endColumns": "41,59", "endOffsets": "23110,24626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\b1e1fee33df799741d8c3dfd4ea908ad\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "364,365,370,377,378,397,398,399,400,401", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "22520,22560,22777,23115,23170,24187,24241,24293,24342,24403", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "22555,22602,22815,23165,23212,24236,24288,24337,24398,24448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\6628e919b5a5d75d6868684452c1191d\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,6,12,20,31,43,49,55,56,57,58,59,362,2407,2413,3796,3804,3819", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,377,550,769,1142,1456,1644,1831,1884,1944,1996,2041,22403,154996,155191,202935,203217,203831", "endLines": "2,11,19,27,42,48,54,55,56,57,58,59,362,2412,2417,3803,3818,3834", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "204,545,764,983,1451,1639,1826,1879,1939,1991,2036,2075,22458,155186,155344,203212,203826,204480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\1b7240aa81db29873b0daf3fe26fc3b1\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "30,75,76,93,94,141,142,267,268,269,270,271,272,273,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,368,369,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,416,450,451,452,453,454,455,456,527,2086,2087,2092,2095,2100,2253,2254,2964,3037,3207,3240,3270,3303", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1082,3072,3144,4441,4506,7718,7787,16341,16411,16479,16551,16621,16682,16756,17999,18060,18121,18183,18247,18309,18370,18438,18538,18598,18664,18737,18806,18863,18915,20075,20147,20223,20288,20347,20406,20466,20526,20586,20646,20706,20766,20826,20886,20946,21006,21065,21125,21185,21245,21305,21365,21425,21485,21545,21605,21665,21724,21784,21844,21903,21962,22021,22080,22139,22707,22742,23328,23383,23446,23501,23559,23617,23678,23741,23798,23849,23899,23960,24017,24083,24117,24152,25236,27506,27573,27645,27714,27783,27857,27929,34604,135763,135880,136147,136440,136707,148650,148722,173140,176038,183873,185604,186604,187286", "endLines": "30,75,76,93,94,141,142,267,268,269,270,271,272,273,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,368,369,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,416,450,451,452,453,454,455,456,527,2086,2090,2092,2098,2100,2253,2254,2969,3046,3239,3260,3302,3308", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1137,3139,3227,4501,4567,7782,7845,16406,16474,16546,16616,16677,16751,16824,18055,18116,18178,18242,18304,18365,18433,18533,18593,18659,18732,18801,18858,18910,18972,20142,20218,20283,20342,20401,20461,20521,20581,20641,20701,20761,20821,20881,20941,21001,21060,21120,21180,21240,21300,21360,21420,21480,21540,21600,21660,21719,21779,21839,21898,21957,22016,22075,22134,22193,22737,22772,23378,23441,23496,23554,23612,23673,23736,23793,23844,23894,23955,24012,24078,24112,24147,24182,25301,27568,27640,27709,27778,27852,27924,28012,34670,135875,136076,136252,136636,136831,148717,148784,173338,176334,185599,186280,187281,187448"}}, {"source": "C:\\newapps\\getlms\\build\\flutter_inappwebview_android\\intermediates\\packaged_res\\debug\\packageDebugResources\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,11,14", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,102,153,210,259,306,353,426,502", "endLines": "2,3,4,5,6,7,10,13,21", "endColumns": "46,50,56,48,46,46,12,12,12", "endOffsets": "97,148,205,254,301,348,421,497,949"}, "to": {"startLines": "444,445,446,447,448,521,536,1654,2150", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "27172,27219,27270,27327,27376,34320,35354,106595,140542", "endLines": "444,445,446,447,448,521,538,1656,2157", "endColumns": "46,50,56,48,46,46,12,12,12", "endOffsets": "27214,27265,27322,27371,27418,34362,35422,106666,140989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\e47cbcca7da1ebc3ce75f342c71ff522\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "404", "startColumns": "4", "startOffsets": "24528", "endColumns": "42", "endOffsets": "24566"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\438524f0b0ce8adbcc6349ae5061871d\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2428,2444,2450,3835,3851", "startColumns": "4,4,4,4,4", "startOffsets": "155866,156291,156469,204485,204896", "endLines": "2443,2449,2459,3850,3854", "endColumns": "24,24,24,24,24", "endOffsets": "156286,156464,156748,204891,205018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\2d4414c41de30e1ee8f38e345744105f\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,144,304,305,306,307,308,309,310,371,372,373,414,415,498,509,522,523,528,529,530,1629,1824,1827,1833,1839,1842,1848,1852,1855,1862,1868,1871,1877,1882,1887,1894,1896,1902,1908,1916,1921,1928,1933,1939,1943,1950,1954,1960,1966,1969,1973,1974,2955,2998,3165,3203,3345,3533,3551,3615,3625,3635,3642,3648,3752,3925,3942", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2238,7939,18977,19041,19096,19164,19231,19296,19353,22820,22868,22916,25110,25173,32642,33394,34367,34411,34675,34814,34864,104839,118938,119043,119288,119626,119772,120112,120324,120487,120894,121232,121355,121694,121933,122190,122561,122621,122959,123245,123694,123986,124374,124679,125023,125268,125598,125805,126073,126346,126490,126691,126738,172820,174267,182423,183724,188666,194576,195204,197129,197411,197716,197978,198238,201754,208220,208750", "endLines": "63,144,304,305,306,307,308,309,310,371,372,373,414,415,498,509,522,525,528,529,530,1645,1826,1832,1838,1841,1847,1851,1854,1861,1867,1870,1876,1881,1886,1893,1895,1901,1907,1915,1920,1927,1932,1938,1942,1949,1953,1959,1965,1968,1972,1973,1974,2959,3008,3184,3206,3354,3540,3614,3624,3634,3641,3647,3690,3764,3941,3958", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2306,8003,19036,19091,19159,19226,19291,19348,19405,22863,22911,22972,25168,25231,32675,33446,34406,34546,34809,34859,34907,106272,119038,119283,119621,119767,120107,120319,120482,120889,121227,121350,121689,121928,122185,122556,122616,122954,123240,123689,123981,124369,124674,125018,125263,125593,125800,126068,126341,126485,126686,126733,126789,173000,174663,183147,183868,188993,194819,197124,197406,197711,197973,198233,199656,202201,208745,209313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\61b3d681e86f6668284365451281321d\\transformed\\media-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "143,311,312,313,314,2091,2093,2094,2099,2101", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "7850,19410,19463,19516,19569,136081,136257,136379,136641,136836", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "7934,19458,19511,19564,19617,136142,136374,136435,136702,136898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\01c026ab9d98be5633d940cb3360b193\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "87,88,89,90,246,247,499,510,511,512", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4077,4135,4201,4264,14863,14934,32680,33451,33518,33597", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "4130,4196,4259,4321,14929,15001,32743,33513,33592,33661"}}, {"source": "C:\\newapps\\getlms\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "173,818", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "476,982"}, "to": {"startLines": "1657,1661", "startColumns": "4,4", "startOffsets": "106671,106852", "endLines": "1660,1663", "endColumns": "12,12", "endOffsets": "106847,107016"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\6161dfd1ea2fe3cd70ce4db6aa4d0ec0\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "4,2255,3075,3081", "startColumns": "4,4,4,4", "startOffsets": "261,148789,177696,177907", "endLines": "4,2257,3080,3164", "endColumns": "60,12,24,24", "endOffsets": "317,148929,177902,182418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\960a97546d38b4e4dcc2f407efd20e21\\transformed\\swiperefreshlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "24", "endOffsets": "287"}, "to": {"startLines": "3855", "startColumns": "4", "startOffsets": "205023", "endLines": "3858", "endColumns": "24", "endOffsets": "205189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\ff2e1761bf6d830a22939dcdd34c6743\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,35,36,37,38,45,47,50,7", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,107,168,230,292,2179,2238,2295,2349,2763,2827,2953,356", "endLines": "2,3,4,5,6,35,36,37,44,46,49,52,34", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "102,163,225,287,351,2233,2290,2344,2758,2822,2948,3076,2174"}, "to": {"startLines": "3,95,96,97,98,248,249,250,560,1646,1648,1651,2970", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "209,4572,4633,4695,4757,15006,15065,15122,36878,106277,106341,106467,173343", "endLines": "3,95,96,97,98,248,249,250,566,1647,1650,1653,2997", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12,24", "endOffsets": "256,4628,4690,4752,4816,15060,15117,15171,37287,106336,106462,106590,174262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\903635807199ccfd256d45cbbfe5a9dc\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "276,277,278,286,287,288,367,3697", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "17002,17061,17109,17776,17851,17927,22641,199876", "endLines": "276,277,278,286,287,288,367,3716", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "17056,17104,17160,17846,17922,17994,22702,200666"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\d74419859ffe1ed5089ef337b323fbaa\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "449", "startColumns": "4", "startOffsets": "27423", "endColumns": "82", "endOffsets": "27501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\f57fa92900567520c1e8b40c26679247\\transformed\\work-runtime-2.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "64,65,66,67", "startColumns": "4,4,4,4", "startOffsets": "2311,2376,2446,2510", "endColumns": "64,69,63,60", "endOffsets": "2371,2441,2505,2566"}}]}]}