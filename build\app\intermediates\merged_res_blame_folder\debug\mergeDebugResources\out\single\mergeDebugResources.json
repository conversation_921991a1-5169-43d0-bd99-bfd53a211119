[{"merged": "com.get_lms_flutter.app-debug-62:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.get_lms_flutter.app-main-57:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.get_lms_flutter.app-debug-62:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.get_lms_flutter.app-main-57:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.get_lms_flutter.app-debug-62:/drawable_notification_icon.png.flat", "source": "com.get_lms_flutter.app-main-57:/drawable/notification_icon.png"}, {"merged": "com.get_lms_flutter.app-debug-62:/raw_notification.mp3.flat", "source": "com.get_lms_flutter.app-main-57:/raw/notification.mp3"}, {"merged": "com.get_lms_flutter.app-debug-62:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.get_lms_flutter.app-main-57:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.get_lms_flutter.app-debug-62:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.get_lms_flutter.app-main-57:/mipmap-hdpi/ic_launcher.png"}, {"merged": "com.get_lms_flutter.app-debug-62:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.get_lms_flutter.app-main-57:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.get_lms_flutter.app-debug-62:/drawable-v21_launch_background.xml.flat", "source": "com.get_lms_flutter.app-main-57:/drawable-v21/launch_background.xml"}]