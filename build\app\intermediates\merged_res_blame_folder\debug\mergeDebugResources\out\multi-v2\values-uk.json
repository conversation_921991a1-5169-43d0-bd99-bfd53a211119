{"logs": [{"outputFile": "com.get_lms_flutter.app-mergeDebugResources-60:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\6ceec6bff55e07b3979c7c6841be58f3\\transformed\\jetified-play-services-basement-18.2.0\\res\\values-uk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "58", "startColumns": "4", "startOffsets": "5903", "endColumns": "145", "endOffsets": "6044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\2d4414c41de30e1ee8f38e345744105f\\transformed\\preference-1.2.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,267,348,490,659,744", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "174,262,343,485,654,739,822"}, "to": {"startLines": "68,79,83,84,87,88,89", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7118,7990,8413,8494,8819,8988,9073", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "7187,8073,8489,8631,8983,9068,9151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\1b7240aa81db29873b0daf3fe26fc3b1\\transformed\\core-1.13.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "29,30,31,32,33,34,35,86", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2829,2929,3031,3132,3233,3338,3443,8718", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "2924,3026,3127,3228,3333,3438,3551,8814"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\1192a99ead1dcf451562e1b569f7ae4a\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,262,333,413,486,579,668", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "124,189,257,328,408,481,574,663,738"}, "to": {"startLines": "70,71,72,73,74,75,76,77,78", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7302,7376,7441,7509,7580,7660,7733,7826,7915", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "7371,7436,7504,7575,7655,7728,7821,7910,7985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\ba0a3358a2f1527f1f0bb638f3aae4bd\\transformed\\appcompat-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,2906"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,85", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,8636", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,8713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\146e143cf420799dfe6460ba9202d0fe\\transformed\\jetified-facebook-login-17.0.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,235,315,407,496,609,729,818,907,1007,1092,1177,1277", "endColumns": "87,91,79,91,88,112,119,88,88,99,84,84,99,113", "endOffsets": "138,230,310,402,491,604,724,813,902,1002,1087,1172,1272,1386"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47,48,49", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3556,3644,3736,3816,3908,3997,4110,4230,4319,4408,4508,4593,4678,4778", "endColumns": "87,91,79,91,88,112,119,88,88,99,84,84,99,113", "endOffsets": "3639,3731,3811,3903,3992,4105,4225,4314,4403,4503,4588,4673,4773,4887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\aafc2bc7e0972df9911e370da464a911\\transformed\\jetified-play-services-base-18.0.1\\res\\values-uk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,579,685,835,960,1071,1172,1336,1438,1596,1717,1860,1998,2064,2121", "endColumns": "103,158,122,105,149,124,110,100,163,101,157,120,142,137,65,56,83", "endOffsets": "296,455,578,684,834,959,1070,1171,1335,1437,1595,1716,1859,1997,2063,2120,2204"}, "to": {"startLines": "50,51,52,53,54,55,56,57,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4892,5000,5163,5290,5400,5554,5683,5798,6049,6217,6323,6485,6610,6757,6899,6969,7030", "endColumns": "107,162,126,109,153,128,114,104,167,105,161,124,146,141,69,60,87", "endOffsets": "4995,5158,5285,5395,5549,5678,5793,5898,6212,6318,6480,6605,6752,6894,6964,7025,7113"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.8\\transforms\\01c026ab9d98be5633d940cb3360b193\\transformed\\browser-1.8.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,392", "endColumns": "109,106,119,107", "endOffsets": "160,267,387,495"}, "to": {"startLines": "69,80,81,82", "startColumns": "4,4,4,4", "startOffsets": "7192,8078,8185,8305", "endColumns": "109,106,119,107", "endOffsets": "7297,8180,8300,8408"}}]}]}